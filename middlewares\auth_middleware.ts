

/// authentication middleware
import { Request, Response, NextFunction } from 'express';

export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {

    /// check if user is authenticated using auth token
    if (!req.headers.authorization) {
        return res.status(401).json({ message: 'Unauthorized' });
    }

    /// check barear token
    const token = req.headers.authorization.split(' ')[1];

    /// verify jwt token
    jwt.verify(token, process.env.JWT_SECRET, (err: any, decoded: any) => {
        if (err) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        req.body.userId = decoded.id;
        next();
    });




    next();
};